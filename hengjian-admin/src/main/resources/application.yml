# 项目相关配置
hengjian:
  # 名称
  name: HengJian-Distribution
  # 版本
  version: 5.0.0-BETA
  # 版权年份
  copyrightYear: 2023
  # 实例演示开关
  demoEnabled: true
  # 获取ip地址开关
  addressEnabled: true

captcha:
  enable: false
  # 页面 <参数设置> 可开启关闭 验证码校验
  # 验证码类型 math 数组计算 char 字符验证
  type: CHAR
  # line 线段干扰 circle 圆圈干扰 shear 扭曲干扰
  category: CIRCLE
  # 数字验证码位数
  numberLength: 1
  # 字符验证码长度
  charLength: 4

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /zsmall-api
  # undertow 配置
  undertow:
    # HTTP post内容的最大大小。当值为-1时，默认值为大小是无限的
    max-http-post-size: -1
    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理
    # 每块buffer的空间大小,越小的空间被利用越充分
    buffer-size: 512
    # 是否分配的直接内存
    direct-buffers: true
    threads:
      # 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个CPU核心一个线程
      io: 8
      # 阻塞任务线程池, 当执行类似servlet请求阻塞操作, undertow会从这个线程池中取得线程,它的值设置取决于系统的负载
      worker: 256

# 日志配置
logging:
  level:
    com.hengjian: @logging.level@
    org.springframework: warn
    com.ijpay.payoneer: debug
    com.zsmall: @logging.level@
  config: classpath:logback-plus.xml


# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
#  main:
#    allow-bean-definition-overriding: true

  application:
    name: HengJian-Distribution
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: @profiles.active@
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 20MB
      # 设置总上传的文件大小
      max-request-size: 40MB
  mvc:
    format:
      date-time: yyyy-MM-dd HH:mm:ss
  jackson:
    # 日期格式化
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      # 格式化输出
      indent_output: false
      # 忽略无法转换的对象
      fail_on_empty_beans: false
    deserialization:
      # 允许对象忽略json中不存在的属性
      fail_on_unknown_properties: false

# Sa-Token配置
sa-token:
  # token名称 (同时也是cookie名称)
  token-name: Authorization
  # token有效期 设为一天 (必定过期) 单位: 秒
  timeout: 86400
  # 多端不同 token 有效期 可查看 LoginHelper.loginByDevice 方法自定义
  # token最低活跃时间 (指定时间无操作就过期) 单位: 秒
  active-timeout: 86400
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: false
  # 是否尝试从header里读取token
  is-read-header: true
  # 是否尝试从cookie里读取token
  is-read-cookie: false
  # token前缀
  token-prefix: "Bearer"
  # jwt秘钥
  jwt-secret-key: vrKiM4EoP1kM7nyjWIbOwp7aKW

# security配置
security:
  # 排除路径
  excludes:
    # 静态资源
    - /*.html
    - /**/*.html
    - /**/*.css
    - /**/*.js
    # 公共路径
    - /favicon.ico
    - /error
    # swagger 文档配置
    - /*/api-docs
    - /*/api-docs/**
    # actuator 监控配置
    - /actuator
    - /actuator/**
    - /webhook/tracking17/notify
    - /business/productSkuStock/testStock
    - /business/worldLocation/listForSelect
    - /business/productActivity/testActivity
    - /mp/product/searchProductCategoryTree
    - /mp/product/searchProductPage
    - /business/mpConfig/show
    - /system/backgroundHome/queryStaticArticles
    - /mp/product/getProductQAPage
    - /order/tripartite/**
    - /distributor/salesChannel/**
    - /test/feature/**
    - /payV2/**
    - /open/api/**
    - /business/**
    - /logistics/tracking/ordersTrackingBack
    - /system/localeMessages/allJsonData
    - /paymentExtension/**
    - /system/countryCurrency/siteCurrencyList
    - business/orders/payOrderTemp
#    - /business/orders/payOrder
#    - /business/product

# 多租户配置
tenant:
  # 是否开启
  enable: true
  # 排除表
  excludes:
    - sys_menu
    - sys_tenant
    - sys_tenant_package
    - sys_role_dept
    - sys_role_menu
    - sys_user_post
    - sys_user_role
    - sys_config
    - sys_dict_type
    - sys_dict_data
    - sys_oss_config
    - sys_notice
    - sys_notice_tenant
    - blog_article
    - blog_category
    - product_category
    - world_location
    - warehouse_address
    - product_sku_price
    - product_sku_price_log
    - product_sku_price_rule
    - product_sku_attachment
    - product_sku_price_rule_item
    - product_sku_price_rule_relation
    - product_attribute
    - product_attachment
    - product_category_relation
    - product_sku_attachment
    - product_sku_attribute
    - product_sku_detail
    - product_category
    - product_label
    - product_label_relation
    - product_review_change_detail
    - product_review_record
    - product_sku_wholesale_price
    - product_sku_wholesale_price_log
    - product_wholesale_detail
    - product_wholesale_tiered_price
    - product_wholesale_tiered_price_log
    - product_question_answer_log
    - shipping_returns
    - tracking17_carrier
    - shopify_extra_properties
    - view_product_sku_sales
    - view_product_stock
    - order_logistics_info
    - order_address_info
    - order_item_price
    - order_item_tracking_record
    - order_refund_attachment
    - order_refund_item
    - order_refund_logistics
    - order_refund_rule
    - order_attachment
    - order_extra_info
    - transaction_receipt_attachment
    - logistics_template_rate_rule
    - logistics_warehouse_relation
    - logistics_rate_country_relation
    - shipping_service
    - task_sku_price_change
    - transactions_orders
    - transactions_order_refund
    - third_channel_fulfillment_record
    - bill_abstract
    - bill_abstract_log
    - bill_abstract_detail
    - bill_abstract_detail_log
    - bill_relation
    - bill_relation_log
    - bill_relation_detail
    - bill_relation_detail_log
    - tenant_receipt_account_attachment
    - tenant_receipt_account_credit
    - tenant_receipt_account_payoneer
    - task_stock_sync
    - product_channel_control
    - product_activity_buyout
    - product_activity_checkout
    - product_activity_price
    - product_activity_price_item
    - product_activity_stock
    - product_activity_stock_item
    - product_activity_stock_item_log
    - product_activity_stock_log
    - product_activity_stock_lock
    - transactions_product_activity_checkout
    - transactions_product_activity_item
    - wholesale_intention_order_address
    - wholesale_intention_order_logistics
    - transactions_wholesale_intention_order
    - china_spot_product_sku_attachment
    - china_spot_product_sku_stock
    - china_spot_product_sku_price
    - tenant_sup_settle_in_basic_attachment
    - tenant_sup_settle_in_basic_attachment_log
    - tenant_sup_settle_in_basic_log
    - tenant_sup_settle_in_contact
    - tenant_sup_settle_in_contact_log
    - tenant_sup_settle_in_extended
    - tenant_sup_settle_in_extended_attachment
    - tenant_sup_settle_in_extended_attachment_log
    - tenant_sup_settle_in_extended_log
    - marketplace_config
    - marketplace_activity_config
    - marketplace_activity_config_item
    - user_feedback_attachment
    - amazon_app_config
    - channel_extend_rakuten_genre
    - common_regions
    - sys_inf
    - sys_api
    - sys_api_inf
    - member_rule_relation
#    - member_level
    - member_rule_relation_log
    - rule_level_product_price
    - member_discount
    - conf_zip
    - warehouse_delivery_country
    - warehouse_admin_delivery_country
    - site_country_currency
    - order_es_processing_failed
    - payment_approval
    - supplier_product_activity
    - supplier_product_activity_stock
    - supplier_product_activity_price
    - supplier_product_activity_review_record
    - distributor_product_activity
    - distributor_product_activity_price
    - distributor_product_activity_stock
    - product_activity_stock_lock_release_log
    - announcement
    - announcement_oss
    - announcement_read

# MyBatisPlus配置
# https://baomidou.com/config/
mybatis-plus:
  # 不支持多包, 如有需要可在注解配置 或 提升扫包等级
  # 例如 com.**.**.mapper
  mapperPackage: com.hengjian.**.mapper
  # 对应的 XML 文件位置
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.hengjian.**.domain
  # 启动时是否检查 MyBatis XML 文件的存在，默认不检查
  checkConfigLocation: false
  configuration:
    # 自动驼峰命名规则（camel case）映射
    mapUnderscoreToCamelCase: true
    # MyBatis 自动映射策略
    # NONE：不启用 PARTIAL：只对非嵌套 resultMap 自动映射 FULL：对所有 resultMap 自动映射
    autoMappingBehavior: FULL
    # MyBatis 自动映射时未知列或未知属性处理策
    # NONE：不做处理 WARNING：打印相关警告 FAILING：抛出异常和详细信息
    autoMappingUnknownColumnBehavior: NONE
    # 更详细的日志输出 会有性能损耗 org.apache.ibatis.logging.stdout.StdOutImpl
    # 关闭日志记录 (可单纯使用 p6spy 分析) org.apache.ibatis.logging.nologging.NoLoggingImpl
    # 默认日志输出 org.apache.ibatis.logging.slf4j.Slf4jImpl
    logImpl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    # 是否打印 Logo banner
    banner: true
    dbConfig:
      # 主键类型
      # AUTO 自增 NONE 空 INPUT 用户输入 ASSIGN_ID 雪花 ASSIGN_UUID 唯一 UUID
      # 如需改为自增 需要将数据库表全部设置为自增
      idType: ASSIGN_ID
      # 逻辑已删除值
      logicDeleteValue: 2
      # 逻辑未删除值
      logicNotDeleteValue: 0
      # 字段验证策略之 insert,在 insert 的时候的字段验证策略
      # IGNORED 忽略 NOT_NULL 非NULL NOT_EMPTY 非空 DEFAULT 默认 NEVER 不加入 SQL
      insertStrategy: NOT_NULL
      # 字段验证策略之 update,在 update 的时候的字段验证策略
      updateStrategy: NOT_NULL
      # 字段验证策略之 select,在 select 的时候的字段验证策略既 wrapper 根据内部 entity 生成的 where 条件
      where-strategy: NOT_NULL

# 数据加密
mybatis-encryptor:
  # 是否开启加密
  enable: true
  # 默认加密算法
  algorithm: BASE64
  # 编码方式 BASE64/HEX。默认BASE64
  encode: BASE64
  # 安全秘钥 对称算法的秘钥 如：AES，SM4
  password:
  # 公私钥 非对称算法的公私钥 如：SM2，RSA
  publicKey:
  privateKey:

# api接口加密
api-decrypt:
  # 是否开启全局接口加密
  enabled: true
  # AES 加密头标识
  headerFlag: encrypt-key
  # 公私钥 非对称算法的公私钥 如：SM2，RSA 使用者请自行更换
  publicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCUPLYnRtjfdw5LLbyu4q1qU4+xYO5UjJ/xcb2HhKnBx+bcylcf2ncWVQcmBz6qvQptomNokrRmqlPZGNz/l+Aiu816NBcgAZy6x2KdqbSIzXm2abgQmLemZmx5AZnt7ALDo+Y+leZOP8uLx8lQTyBXBhDbQ8IouBC1Oi7LxX5hVQIDAQAB
  privateKey: MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAJQ8tidG2N93DkstvK7irWpTj7Fg7lSMn/FxvYeEqcHH5tzKVx/adxZVByYHPqq9Cm2iY2iStGaqU9kY3P+X4CK7zXo0FyABnLrHYp2ptIjNebZpuBCYt6ZmbHkBme3sAsOj5j6V5k4/y4vHyVBPIFcGENtDwii4ELU6LsvFfmFVAgMBAAECgYAbVJI07aAAm1+8RqN6DvrwFpXwmzF1zzOR/Wl3OeNege+zl262xb2dFXIq9bz9F7t7eSwg1KOEJjUtWqMsUKEXHm8ud0pqtgNQ19GoWKA6gsNG7iYuYoBLQ7qMb+OWJJudFelKaKB8ju3YiNdXb7oW3zxmYEer2cx4aSsWSXuYRQJBAMmJka2svwMccbbyuSTlm1t4vgGP6qCp9nXGVt2do4eqQtj9mb5OqaDJ9j4CTEWnlZkmjI9VondjHa25FlT7oP8CQQC8S9AmdopBxMOgfyTFtmPsC1DlIqZN2HiUFnvAXV4nttq4K/KUB6dYbKcAs5Tjv4G7Jv6gID/L7pwcimYKXymrAkEAv4DLPp6BOrGSlGb76sWK5FZNJircpiIaoTsAW8FpHIZ60rnXl3Aj2zdNlwqT8zQVANwSgDi9EnjKQMM5JzObfQJAOVAqL0rtkk897GBG5Rc7GuHm4ZfGXvOA4DJnwImq2xPBR0W5NG2P9k48Fu6SZzAnYLvhzQOD56LdSaKmpImKuQJAL81uoHKKoeeljXeCx+yvuNm5/IlucJHF0WafKPigcufEXPkeWBiu5pDsVVlgWLw0j4MxonQb2Iy4Mfl2LxKajg==

# Swagger配置
springdoc:
  api-docs:
    # 是否开启接口文档
    enabled: true
  swagger-ui:
    # 持久化认证数据
    persistAuthorization: true
  info:
    # 标题
    title: '标题：HengJian-Distribution多租户管理系统_接口文档'
    # 描述
    description: '描述：用于管理集团旗下公司的人员信息,具体包括XXX,XXX模块...'
    # 版本
    version: '版本号: 5.0.0-BETA'
    # 作者信息
    contact:
      name: Lion Li
      email: <EMAIL>
      url: https://gitee.com/dromara/HengJian-Distribution
  components:
    # 鉴权方式配置
    security-schemes:
      apiKey:
        type: APIKEY
        in: HEADER
        name: ${sa-token.token-name}
  #这里定义了两个分组，可定义多个，也可以不定义
  group-configs:
    - group: 1.演示模块
      packages-to-scan: com.hengjian.demo
    - group: 2.通用模块
      packages-to-scan: com.hengjian.web
    - group: 3.系统模块
      packages-to-scan: com.hengjian.system
    - group: 4.代码生成模块
      packages-to-scan: com.hengjian.generator
    - group: 5.业务模块
      packages-to-scan: com.zsmall
      paths-to-match: '/**'
    - group: 6.业务商城模块
      packages-to-scan: com.zsmall
      paths-to-match: '/mp/**'


# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice,/blog/saveBlogArticle,/system/config,/system/localeMessages/add,/announcement
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*,/blog/*,/mp/*

# 全局线程池相关配置
thread-pool:
  # 是否开启线程池
  enabled: true
  # 队列最大长度
  queueCapacity: 128
  # 线程池维护线程所允许的空闲时间
  keepAliveSeconds: 300

--- # 分布式锁 lock4j 全局配置
lock4j:
  # 获取分布式锁超时时间，默认为 3000 毫秒
  acquire-timeout: 3000
  # 分布式锁的超时时间，默认为 30 秒
  expire: 30000

--- # Actuator 监控端点的配置项
management:
  server:
    port: 9595
  endpoints:
    web:
      exposure:
        include: '*' # 方式2: 包括所有端点，注意需要添加引号
#        exclude: shutdown # 排除端点
      base-path: /monitor
  endpoint:
    health:
      show-details: ALWAYS
    logfile:
      external-file: ./logs/sys-console.log
    shutdown:
      enabled: true

--- # websocket
websocket:
  enabled: true
  # 路径
  path: /websocket
  # 设置访问源地址
  allowedOrigins: '*'
